{"views.servers.title": "SFTP 服务器", "views.explorer.title": "文件浏览器", "commands.showInfo": "显示配置信息", "commands.addServer": "添加服务器", "commands.editServer": "编辑服务器", "commands.deleteServer": "删除服务器", "commands.openSettings": "打开设置", "commands.openSettingsEditor": "打开设置编辑器", "commands.refreshServers": "刷新服务器列表", "commands.refreshExplorer": "刷新文件浏览器", "commands.uploadFile": "上传到当前服务器", "commands.uploadToServer": "上传到其他服务器...", "commands.uploadToAllServers": "上传到所有服务器", "commands.deleteRemoteFile": "删除远程文件", "commands.downloadRemoteFile": "下载远程文件", "commands.cancelOperations": "取消操作", "commands.disconnectServer": "断开连接", "commands.connectServer": "连接服务器", "menus.uploadSubmenu": "SFTP 上传", "configuration.title": "SFTP Tools", "commands.connectSSH": "打开 SSH 终端", "commands.deleteRemoteDirectory": "删除远程目录", "configuration.showConfirmDialog.description": "删除文件时是否显示确认对话框", "configuration.showConfirmDialog.markdownDescription": "删除文件或目录时是否显示确认对话框。\n- `true`: 删除前显示确认对话框\n- `false`: 直接删除，不显示确认", "configuration.servers.description": "SFTP 服务器配置", "configuration.servers.name.description": "服务器名称", "configuration.servers.host.description": "主机地址", "configuration.servers.port.description": "端口", "configuration.servers.username.description": "用户名", "configuration.servers.password.description": "密码", "configuration.servers.privateKeyPath.description": "私钥文件路径", "configuration.servers.passphrase.description": "私钥密码短语", "configuration.servers.remotePath.description": "远程路径", "commands.uploadDirectory": "上传目录", "commands.uploadDirectoryToServer": "上传目录到其他服务器...", "commands.uploadDirectoryToAllServers": "上传目录到所有服务器", "configuration.useGlobalConfig.description": "使用全局配置而不是工作区配置", "configuration.useGlobalConfig.markdownDescription": "启用后，所有工作区将使用相同的全局 SFTP 服务器配置。否则，每个工作区都有自己的服务器配置。", "settings.enableAutoUpload": "启用保存时自动上传", "settings.enableAutoUploadDesc": "启用或禁用保存文件时自动上传功能。", "settings.autoUploadShortcutHint": "默认快捷键：Ctrl+S (Mac 上为 Cmd+S)。您可以在 VS Code 的键盘快捷方式首选项中自定义此快捷键 (搜索 'sftp-tools.uploadFile')。", "messages.backupDirDelete": "这是备份目录，确定要删除吗？如需禁用备份功能，可在设置中将备份路径设为空。", "messages.backupDirDeleteConfirm": "删除", "messages.backupDirDeleteCancel": "取消", "messages.backupDisabled": "备份功能未启用，跳过备份操作", "messages.backupPathSet": "备份路径设置为: {0}", "messages.directoryBackedUp": "目录已备份到: {0}", "messages.fileBackedUp": "文件已备份到: {0}", "messages.backupFailed": "备份失败: {0}"}
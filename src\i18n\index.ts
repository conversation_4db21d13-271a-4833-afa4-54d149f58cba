import * as vscode from 'vscode';

const zh = {
    home: {
        isActive: 'SFTP Tools 扩展正在激活...',
        isReady : 'SFTP Tools 扩展已准备就绪。'
    },
    view: {
        servers: 'SFTP 服务器',
        explorer: '文件浏览器'
    },
    settings: {
        title: 'SFTP 设置',
        otherSettings: '其他设置',
        serverSettings: '服务器设置',
        showDeleteConfirm: '删除前确认',
        showDeleteConfirmDesc: '删除远程文件或文件夹前是否显示确认对话框',
        uploadOrDeleteBackup: '上传或删除时备份文件到目录',
        uploadOrDeleteBackupDesc: '上传或删除时是否备份文件,空表示不备份，默认备份到填写远程目录的 backup-sftp 目录',
        serverName: '服务器名称',
        serverNameRequired: '服务器名称不能为空',
        serverNameExists: '服务器名称已存在',
        enterServerName: '输入服务器名称',
        enterHost: '输入主机地址',
        enterPort: '输入端口',
        enterUsername: '输入用户名',
        enterPassword: '输入密码',
        selectAuthType: '选择认证方式',
        selectPrivateKey: '选择私钥文件',
        needPassphrase: '私钥文件是否需要密码短语？',
        enterPassphrase: '输入私钥密码短语',
        enterRemotePath: '输入远程路径',
        enterLocalPath: '输入本地工作区目录,/ 为当前工作区',
        host: '主机地址',
        port: '端口',
        username: '用户名',
        password: '密码',
        privateKey: '私钥文件',
        passphrase: '密码短语',
        remotePath: '远程目录',
        localPath: '本地工作区目录',
        paths: '路径设置',
        authType: '认证方式',
        authPassword: '密码认证',
        authPrivateKey: '密钥文件',
        addServer: '添加服务器',
        saveAll: '保存全部',
        emptyTip: '还没有配置任何服务器',
        deleteConfirm: '确定要删除吗？',
        delete: '删除',
        yes: '是',
        no: '否',
        clickAddServerTip: '点击 添加服务器 按钮开始配置',
        clickSaveAllTip: '配置完成后点击 保存全部 按钮保存更改',
        configureNow: '配置服务器',
        useGlobalConfig: '使用全局配置',
        useGlobalConfigDesc: '启用后，所有工作区将使用相同的服务器配置；禁用后，每个工作区有独立配置',
        enterPrivateKeyPath: '输入或选择私钥文件路径',
        enableShortcutUpload: '启用快捷键上传',
        enableShortcutUploadDesc: '启用或禁用通过快捷键上传文件。',
        enableAutoUpload: '启用保存时自动上传',
        enableAutoUploadDesc: '启用或禁用保存文件时自动上传功能。',
        autoUploadShortcutHint: '默认快捷键：Ctrl+S (Mac 上为 Cmd+S)。您可以在 VS Code 的键盘快捷方式首选项中自定义此快捷键 (搜索 \'sftp-tools.uploadFile\')。'
    },
    status: {
        uploading: '正在上传...',
        uploadSuccess: '上传成功',
        uploadFailed: '上传失败',
        downloading: '正在下载...',
        downloadSuccess: '下载成功',
        downloadFailed: '下载失败',
        connecting: '正在连接...',
        connected: '已连接',
        disconnected: '已断开连接',
        operationCancelled: '操作已取消',
        noWorkspace: '没有找到工作区',
        directoryCreated: '目录已创建',
        directoryCreateFailed: '创建目录失败',
        uploadingDirectory: '正在上传目录...',
        uploadDirectorySuccess: '目录上传成功',
        directoryDeleted: '目录已删除',
        fileDeleted: '文件已删除',
        uploadingToServer: '正在上传到 {0}',
        settingsSaved: '设置已保存',
        configMigrated: '已成功将旧版配置迁移到新版格式。'
    },
    messages: {
        confirmDelete: '确定要删除{0}吗？',
        pathNotFound: '路径不存在',
        uploadComplete: '上传完成',
        downloadComplete: '下载完成',
        operationFailed: '操作失败: {0}',
        noServersConfigured: '还没有配置任何服务器',
        configureNow: '是否现在配置？',
        selectServer: '选择服务器',
        uploadPartialSuccess: '上传完成，{0} 个成功，{1} 个失败。请查看输出日志了解详情。',
        uploadToServer: '上传到 {0}',
        uploadingToServer: '正在上传到 {0}...',
        uploadingProgress: '正在上传到 {0} ({1}/{2})',
        serverConnected: '已连接到 {0}',
        serverDisconnected: '已断开与 {0} 的连接',
        autoInputPasswordPrompt: '是否自动输入保存的密码？',
        sshConnectionFailed: 'SSH 连接失败: {0}',
        noServer: '没有连接到服务器',
        settingsSaveFailed: '保存失败: 无法写入配置文件',
        backupDirDelete: '这是备份目录，确定要删除吗？如需禁用备份功能，可在设置中将备份路径设为空。',
        backupDirDeleteConfirm: '删除',
        backupDirDeleteCancel: '取消',
        backupDisabled: '备份功能未启用，跳过备份操作',
        backupPathSet: '备份路径设置为: {0}',
        directoryBackedUp: '目录已备份到: {0}',
        fileBackedUp: '文件已备份到: {0}',
        backupFailed: '备份失败: {0}',
        pleaseInputPassword: '请在终端中输入密码',
        shortcutUploadDisabled: '快捷键上传功能已在设置中禁用。'
    },
    sftpManagerLogs: { // 新增 SFTP Manager 日志
        attemptingConnect: '尝试连接到 {0}',
        sshClientReady: 'SSH 客户端已准备就绪。',
        sftpSessionError: 'SFTP 会话错误: {0}',
        sftpSessionEstablished: 'SFTP 会话已建立。',
        sshClientError: 'SSH 客户端错误: {0}',
        sshClientEnded: 'SSH 客户端连接被远程主机关闭。',
        sshClientClosed: 'SSH 客户端连接已关闭。发生错误: {0}',
        sshClientTimeout: 'SSH 客户端连接超时。'
    }
};

const en = {
    home: {
        isActive: 'SFTP Tools extension is activating...',
        isReady : 'SFTP Tools extension is ready to use.'
    },
    view: {
        servers: 'SFTP Servers',
        explorer: 'File Explorer'
    },
    settings: {
        title: 'SFTP Settings',
        otherSettings: 'Other Settings',
        serverSettings: 'Server Settings',
        showDeleteConfirm: 'Confirm before delete',
        showDeleteConfirmDesc: 'Show confirmation dialog before deleting remote files or folders',
        uploadOrDeleteBackup: 'Backup when upload or delete file to directory',
        uploadOrDeleteBackupDesc: 'Backup files when uploading or deleting, empty means no backup, default backup to backup-sftp directory in the remote directory',
        serverName: 'Server Name',
        serverNameRequired: 'Server name is required',
        serverNameExists: 'Server name already exists',
        enterServerName: 'Enter server name',
        enterHost: 'Enter host address',
        enterPort: 'Enter port',
        enterUsername: 'Enter username',
        enterPassword: 'Enter password',
        selectAuthType: 'Select authentication type',
        selectPrivateKey: 'Select private key file',
        needPassphrase: 'Does the private key require a passphrase?',
        enterPassphrase: 'Enter private key passphrase',
        enterRemotePath: 'Enter remote path',
        enterLocalPath: 'Enter local workspace path, / is the current workspace',
        host: 'Host',
        port: 'Port',
        username: 'Username',
        password: 'Password',
        privateKey: 'Private Key File',
        passphrase: 'Passphrase',
        remotePath: 'Remote Path',
        localPath: 'Local Workspace Path',
        paths: 'Path Settings',
        authType: 'Authentication Type',
        authPassword: 'Password',
        authPrivateKey: 'Private Key',
        addServer: 'Add Server',
        saveAll: 'Save All',
        emptyTip: 'No servers configured yet',
        deleteConfirm: 'Are you sure you want to delete?',
        delete: 'Delete',
        yes: 'Yes',
        no: 'No',
        clickAddServerTip: 'Click the Add Server button to start configuring',
        clickSaveAllTip: 'Click Save All button to save changes after configuration',
        configureNow: 'Configure Server',
        useGlobalConfig: 'Use global configuration',
        useGlobalConfigDesc: 'When enabled, all workspaces will use the same server configurations; when disabled, each workspace has its own configuration',
        enterPrivateKeyPath: 'Enter or select private key file path',
        enableShortcutUpload: 'Enable Shortcut Upload',
        enableShortcutUploadDesc: 'Enable or disable file upload via shortcut.',
        enableAutoUpload: 'Enable Auto Upload on Save',
        enableAutoUploadDesc: 'Enable or disable automatic file upload when saving.',
        autoUploadShortcutHint: 'Default shortcut: Ctrl+S (Cmd+S on Mac). You can customize this in VS Code\'s Keyboard Shortcuts preferences (search for \'sftp-tools.uploadFile\').'
    },
    status: {
        uploading: 'Uploading...',
        uploadSuccess: 'Upload successful',
        uploadFailed: 'Upload failed',
        downloading: 'Downloading...',
        downloadSuccess: 'Download successful',
        downloadFailed: 'Download failed',
        connecting: 'Connecting...',
        connected: 'Connected',
        disconnected: 'Disconnected',
        operationCancelled: 'Operation cancelled',
        noWorkspace: 'No workspace found',
        directoryCreated: 'Directory created',
        directoryCreateFailed: 'Failed to create directory',
        uploadingDirectory: 'Uploading directory...',
        uploadDirectorySuccess: 'Directory upload successful',
        directoryDeleted: 'Directory deleted',
        fileDeleted: 'File deleted',
        uploadingToServer: 'Uploading to {0}',
        settingsSaved: 'Settings saved',
        configMigrated: 'Successfully migrated configuration from old version to new format.'
    },
    messages: {
        confirmDelete: 'Are you sure you want to delete {0}?',
        pathNotFound: 'Path not found',
        uploadComplete: 'Upload complete',
        downloadComplete: 'Download complete',
        operationFailed: 'Operation failed: {0}',
        noServersConfigured: 'No servers configured',
        configureNow: 'Configure now?',
        selectServer: 'Select server',
        uploadPartialSuccess: 'Upload completed with {0} successes and {1} failures. Check output for details.',
        uploadToServer: 'Upload to {0}',
        uploadingToServer: 'Uploading to {0}...',
        uploadingProgress: 'Uploading to {0} ({1}/{2})',
        serverConnected: 'Connected to {0}',
        serverDisconnected: 'Disconnected from {0}',
        autoInputPasswordPrompt: 'Do you want to auto-input the saved password?',
        sshConnectionFailed: 'SSH connection failed: {0}',
        noServer: 'No server connected',
        settingsSaveFailed: 'Save failed: Cannot write to config file',
        backupDirDelete: 'This is a backup directory, are you sure you want to delete it? To disable backup, you can set the backup path to empty in settings.',
        backupDirDeleteConfirm: 'Delete',
        backupDirDeleteCancel: 'Cancel',
        backupDisabled: 'Backup is disabled, skipping backup operation',
        backupPathSet: 'Backup path is set to: {0}',
        directoryBackedUp: 'Directory backed up to: {0}',
        fileBackedUp: 'File backed up to: {0}',
        backupFailed: 'Backup failed: {0}',
        pleaseInputPassword: 'Please enter your password in the terminal',
        shortcutUploadDisabled: 'Shortcut upload is disabled in settings.'
    },
    sftpManagerLogs: { // Add SFTP Manager logs
        attemptingConnect: 'Attempting to connect to {0}',
        sshClientReady: 'SSH client ready.',
        sftpSessionError: 'SFTP session error: {0}',
        sftpSessionEstablished: 'SFTP session established.',
        sshClientError: 'SSH client error: {0}',
        sshClientEnded: 'SSH client connection ended by remote.',
        sshClientClosed: 'SSH client connection closed. Had error: {0}',
        sshClientTimeout: 'SSH client connection timeout.'
    }
};

export function getLocaleText() {
    // 获取 VS Code 的语言设置
    const locale = vscode.env.language;
    return locale.toLowerCase().startsWith('zh') ? zh : en;
} 
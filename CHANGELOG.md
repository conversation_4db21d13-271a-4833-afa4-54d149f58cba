# 更新日志

[English](CHANGELOG_EN.md) | 简体中文

## [1.1.6] - 2025-05-06
- 新增：快捷键上传功能现在可以通过全局设置页面的开关进行配置。
- 优化：调整 `Ctrl+S` (Cmd+S) 快捷键上传行为，使其响应新的全局设置。
## [1.1.5] - 2025-04-07
- 优化 ssh 终端自动连接

## [1.1.4] - 2025-04-07
- 优化 ssh 终端连接操作方式
- 优化远程目录的排序方式


## [1.1.3] - 2025-03-26
- 修复打开远程文件时，某些文件后缀无法打开的问题

## [1.1.2] - 2025-03-21
- 独立其他配置到配置文件
- 增加上传替换或删除前的 备份 操作
- 优化提示，输出的 多语言

## [1.1.1] - 2025-03-14
- 增加readme，changelog的英文支持

## [1.0.9] - 2024-02-24
- 修复 ssh2 模块打包问题

## [1.0.8] - 2024-02-24
- 优化说明文件

## [1.0.7] - 2025-02-24

### 新增
- 增加 ssh 连接功能
- 增加上传目录功能
- 修复打包问题


## [1.0.6] - 2024-03-14

### 新增
- 添加多语言支持
- 支持中英文界面
- 根据 VS Code 语言设置自动切换

## [1.0.5] - 2024-03-14

### 修复
- 修复插件激活问题
- 修复视图提供程序注册问题
- 优化设置页面按钮样式

## [1.0.4] - 2025-02-24

### 新增
- 修复权限问题导致的插件无法启动
- 优化项目资源大小

## [1.0.3] - 2025-02-24

### 新增
- 修复上传路径问题
- 优化配置页面
- 优化输出日志
- 增加状态栏提示

## [1.0.2] - 2024-03-14

### 改进
- 优化了上传功能的用户体验
- 增加了本地文件上传到服务器的功能
- 改进了状态栏提示信息
- 增加了中文界面支持

### 修复
- 修复了设置编辑器无法打开的问题
- 修复了服务器断开连接后的状态显示问题

## [1.0.1] - 2024-03-13

### 新增
- 支持上传到多个服务器
- 增加了服务器连接状态显示
- 增加了文件下载功能
- 增加了文件删除功能

### 改进
- 优化了服务器配置界面
- 改进了错误提示信息

## [1.0.0] - 2024-03-12

### 初始版本
- 基本的 SFTP 功能实现
- 多服务器管理
- 文件浏览器
- 远程文件编辑
- 文件上传/下载

### 新增
- ✨ 支持多服务器管理
- 🔒 支持密码和密钥认证
- 📁 远程文件浏览器
- 📝 文件编辑与保存
- 🔄 自动上传功能
- 🖥️ 一键 SSH 连接
- 📋 服务器配置管理界面

### 改进
- 优化了文件传输性能
- 改进了错误处理
- 增加了详细的操作日志
- 支持二进制文件传输

### 修复
- 修复了文件路径处理问题
- 修复了编码相关问题
- 修复了连接稳定性问题
{"views.servers.title": "SFTP Servers", "views.explorer.title": "File Explorer", "commands.showInfo": "Show Configuration Info", "commands.addServer": "Add Server", "commands.editServer": "Edit Server", "commands.deleteServer": "Delete Server", "commands.openSettings": "Open Settings", "commands.openSettingsEditor": "Open Settings Editor", "commands.refreshServers": "Refresh Servers", "commands.refreshExplorer": "Refresh Explorer", "commands.uploadFile": "Upload to Current Server", "commands.uploadToServer": "Upload to Other Server...", "commands.uploadToAllServers": "Upload to All Servers", "commands.deleteRemoteFile": "Delete Remote File", "commands.downloadRemoteFile": "Download Remote File", "commands.cancelOperations": "Cancel Operations", "commands.disconnectServer": "Disconnect Server", "commands.connectServer": "Connect to Server", "menus.uploadSubmenu": "SFTP Upload", "configuration.title": "SFTP Tools", "commands.connectSSH": "Open SSH Terminal", "commands.deleteRemoteDirectory": "Delete Remote Directory", "configuration.showConfirmDialog.description": "Show confirmation dialog when deleting files", "configuration.showConfirmDialog.markdownDescription": "Show confirmation dialog when deleting files or directories.\n- `true`: Show confirmation dialog before deletion\n- `false`: Delete without confirmation", "configuration.servers.description": "SFTP server configurations", "configuration.servers.name.description": "Server name", "configuration.servers.host.description": "Server host", "configuration.servers.port.description": "Server port", "configuration.servers.username.description": "Username", "configuration.servers.password.description": "Password", "configuration.servers.privateKeyPath.description": "Private key file path", "configuration.servers.passphrase.description": "Private key passphrase", "configuration.servers.remotePath.description": "Remote path", "commands.uploadDirectory": "Upload Directory", "commands.uploadDirectoryToServer": "Upload Directory to Other Server...", "commands.uploadDirectoryToAllServers": "Upload Directory to All Servers", "configuration.useGlobalConfig.description": "Use global configuration instead of workspace configuration", "configuration.useGlobalConfig.markdownDescription": "When enabled, all workspaces will use the same global SFTP server configurations. Otherwise, each workspace has its own server configurations.", "settings.enableAutoUpload": "Enable Auto Upload on Save", "settings.enableAutoUploadDesc": "Enable or disable automatic file upload when saving.", "settings.autoUploadShortcutHint": "Default shortcut: Ctrl+S (Cmd+S on Mac). You can customize this in VS Code's Keyboard Shortcuts preferences (search for 'sftp-tools.uploadFile').", "messages.backupDirDelete": "This is a backup directory. Are you sure you want to delete it? To disable backup, you can set the backup path to empty in settings.", "messages.backupDirDeleteConfirm": "Delete", "messages.backupDirDeleteCancel": "Cancel", "messages.backupDisabled": "Backup feature is disabled, skipping backup operation", "messages.backupPathSet": "Backup path is set to: {0}", "messages.directoryBackedUp": "Directory backed up to: {0}", "messages.fileBackedUp": "File backed up to: {0}", "messages.backupFailed": "Backup failed: {0}"}
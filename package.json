{"name": "sftp-tools", "displayName": "SFTP Tools", "description": "A powerful SFTP/FTP client for VS Code with an intuitive interface", "publisher": "<PERSON><PERSON><PERSON>", "version": "1.1.6", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "icon": "media/sftp.png", "galleryBanner": {"color": "#C80000", "theme": "dark"}, "keywords": ["sftp", "ftp", "remote", "upload", "download", "sync", "file transfer"], "categories": ["Other", "SCM Providers"], "repository": {"type": "git", "url": "https://github.com/ayuayue/sftp-tools.git"}, "bugs": {"url": "https://github.com/ayuayue/sftp-tools/issues"}, "homepage": "https://github.com/ayuayue/sftp-tools#readme", "changelog": "https://github.com/ayuayue/sftp-tools/blob/main/CHANGELOG.md", "engines": {"vscode": "^1.93.0"}, "activationEvents": ["onView:sftp-tools-servers", "onView:sftp-tools-explorer"], "main": "./dist/extension.js", "contributes": {"keybindings": [{"command": "sftp-tools.uploadFile", "key": "ctrl+s", "mac": "cmd+s", "when": "editorTextFocus && sftp-tools.enableShortcutUploadContext"}], "commands": [{"command": "sftp-tools.showInfo", "title": "%commands.showInfo%", "icon": "$(info)"}, {"command": "sftp-tools.addServer", "title": "%commands.addServer%", "icon": "$(add)"}, {"command": "sftp-tools.editServer", "title": "%commands.editServer%", "icon": "$(edit)"}, {"command": "sftp-tools.deleteServer", "title": "%commands.deleteServer%", "icon": "$(trash)"}, {"command": "sftp-tools.openSettings", "title": "%commands.openSettings%", "icon": "$(gear)"}, {"command": "sftp-tools.openSettingsEditor", "title": "%commands.openSettingsEditor%", "icon": "$(gear)"}, {"command": "sftp-tools.refreshServers", "title": "%commands.refreshServers%", "icon": "$(refresh)"}, {"command": "sftp-tools.refreshExplorer", "title": "Refresh Explorer", "icon": "$(refresh)"}, {"command": "sftp-tools.uploadFile", "title": "%commands.uploadFile%", "icon": "$(cloud-upload)"}, {"command": "sftp-tools.openFileNonPreview", "title": "Open File (Non-Preview)"}, {"command": "sftp-tools.uploadToServer", "title": "%commands.uploadToServer%", "icon": "$(cloud-upload)"}, {"command": "sftp-tools.disconnectServer", "title": "%commands.disconnectServer%", "icon": "$(debug-disconnect)"}, {"command": "sftp-tools.connectServer", "title": "%commands.connectServer%", "icon": "$(plug)"}, {"command": "sftp-tools.uploadToAllServers", "title": "%commands.uploadToAllServers%", "icon": "$(cloud-upload)"}, {"command": "sftp-tools.deleteRemoteFile", "title": "%commands.deleteRemoteFile%", "icon": "$(trash)"}, {"command": "sftp-tools.downloadRemoteFile", "title": "%commands.downloadRemoteFile%", "icon": "$(cloud-download)"}, {"command": "sftp-tools.cancelOperations", "title": "%commands.cancelOperations%", "icon": "$(stop-circle)", "category": "SFTP Tools"}, {"command": "sftp-tools.connectSSH", "title": "%commands.connectSSH%", "icon": "$(terminal)"}, {"command": "sftp-tools.deleteRemoteDirectory", "title": "%commands.deleteRemoteDirectory%", "icon": "$(trash)"}, {"command": "sftp-tools.uploadDirectory", "title": "%commands.uploadDirectory%", "icon": "$(cloud-upload)"}, {"command": "sftp-tools.uploadDirectoryToServer", "title": "%commands.uploadDirectoryToServer%", "icon": "$(cloud-upload)"}, {"command": "sftp-tools.uploadDirectoryToAllServers", "title": "%commands.uploadDirectoryToAllServers%", "icon": "$(cloud-upload)"}, {"command": "sftp-tools.disconnectAllServers", "title": "%commands.disconnectAllServers%", "icon": "$(debug-disconnect)"}], "configuration": {"title": "SFTP Tools", "properties": {"sftp-tools.servers": {"type": "array", "description": "%configuration.servers.description%", "scope": "resource", "items": {"type": "object", "required": ["name", "host", "username"], "properties": {"name": {"type": "string", "description": "%configuration.servers.name.description%"}, "host": {"type": "string", "description": "%configuration.servers.host.description%"}, "port": {"type": "number", "default": 22, "description": "%configuration.servers.port.description%"}, "username": {"type": "string", "description": "%configuration.servers.username.description%"}, "password": {"type": "string", "description": "%configuration.servers.password.description%"}, "privateKeyPath": {"type": "string", "description": "%configuration.servers.privateKeyPath.description%"}, "passphrase": {"type": "string", "description": "%configuration.servers.passphrase.description%"}, "remotePath": {"type": "string", "description": "%configuration.servers.remotePath.description%"}}}, "default": []}, "sftp-tools.uploadOrDeleteBackup": {"type": "string", "default": "/", "description": "上传或删除文件时的备份目录路径"}, "sftp-tools.showConfirmDialog": {"type": "boolean", "default": true, "description": "是否显示删除确认对话框"}, "sftp-tools.enableAutoUpload": {"type": "boolean", "default": false, "description": "是否启用保存时自动上传功能"}}}, "menus": {"view/title": [{"command": "sftp-tools.showInfo", "when": "view == sftp-tools-servers", "group": "navigation"}, {"command": "sftp-tools.refreshServers", "when": "view == sftp-tools-servers", "group": "navigation"}, {"command": "sftp-tools.refreshExplorer", "when": "view == sftp-tools-explorer", "group": "navigation"}, {"command": "sftp-tools.openSettingsEditor", "when": "view == sftp-tools-servers", "group": "navigation"}], "view/item/context": [{"command": "sftp-tools.editServer", "when": "view == sftp-tools-servers", "group": "inline"}, {"command": "sftp-tools.deleteServer", "when": "view == sftp-tools-servers", "group": "inline"}, {"command": "sftp-tools.openFileNonPreview", "when": "view == sftp-tools-explorer && viewItem == file", "group": "navigation"}, {"command": "sftp-tools.disconnectServer", "when": "view == sftp-tools-servers && viewItem == activeServer", "group": "inline"}, {"command": "sftp-tools.downloadRemoteFile", "when": "view == sftp-tools-explorer && viewItem == file", "group": "1_modification"}, {"command": "sftp-tools.deleteRemoteFile", "when": "view == sftp-tools-explorer && (viewItem == file || viewItem == directory)", "group": "1_modification"}, {"command": "sftp-tools.connectSSH", "when": "view == sftp-tools-servers", "group": "inline"}], "editor/title": [{"command": "sftp-tools.uploadFile", "when": "resourceScheme == file", "group": "navigation"}], "editor/context": [{"submenu": "sftp-tools.uploadSubmenu", "when": "resourceScheme == file", "group": "sftp-tools@1"}], "explorer/context": [{"submenu": "sftp-tools.uploadSubmenu", "when": "resourceScheme == file", "group": "2_workspace@1"}, {"command": "sftp-tools.deleteRemoteFile", "when": "resourceScheme == 'sftp' && !explorerResourceIsFolder", "group": "2_modification"}, {"command": "sftp-tools.deleteRemoteDirectory", "when": "resourceScheme == 'sftp' && explorerResourceIsFolder", "group": "2_modification"}, {"command": "sftp-tools.downloadRemoteFile", "when": "resourceScheme == 'sftp'", "group": "2_modification"}], "sftp-tools.uploadSubmenu": [{"command": "sftp-tools.uploadFile", "title": "%commands.uploadFile%", "when": "resourceScheme == file && !explorerResourceIsFolder", "group": "upload@1"}, {"command": "sftp-tools.uploadDirectory", "title": "%commands.uploadDirectory%", "when": "resourceScheme == file && explorerResourceIsFolder", "group": "upload@1"}, {"command": "sftp-tools.uploadDirectoryToServer", "title": "%commands.uploadToServer%", "when": "resourceScheme == file && explorerResourceIsFolder", "group": "upload@2"}, {"command": "sftp-tools.uploadDirectoryToAllServers", "title": "%commands.uploadToAllServers%", "when": "resourceScheme == file && explorerResourceIsFolder", "group": "upload@3"}, {"command": "sftp-tools.uploadToServer", "title": "%commands.uploadToServer%", "when": "resourceScheme == file && !explorerResourceIsFolder", "group": "upload@2"}, {"command": "sftp-tools.uploadToAllServers", "title": "%commands.uploadToAllServers%", "when": "resourceScheme == file && !explorerResourceIsFolder", "group": "upload@3"}]}, "viewsContainers": {"activitybar": [{"id": "sftp-tools", "title": "%configuration.title%", "icon": "media/sftp.svg"}]}, "views": {"sftp-tools": [{"id": "sftp-tools-servers", "name": "%views.servers.title%", "type": "tree", "icon": "media/sftp.svg", "contextualTitle": "%views.servers.title%"}, {"id": "sftp-tools-explorer", "name": "%views.explorer.title%", "type": "tree"}]}, "submenus": [{"id": "sftp-tools.uploadSubmenu", "label": "%menus.uploadSubmenu%"}], "l10n": [{"id": "zh-cn", "path": "./package.nls.zh-cn.json"}, {"id": "en", "path": "./package.nls.json"}]}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "dependencies": {"ssh2": "^1.15.0"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/ssh2": "^1.11.19", "@types/vscode": "^1.93.0", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "eslint": "^9.19.0", "ts-loader": "^9.5.2", "typescript": "^5.7.3", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "extensionDependencies": [], "extensionKind": ["workspace"]}